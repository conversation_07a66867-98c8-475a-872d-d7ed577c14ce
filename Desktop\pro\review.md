# 1. 权限原因说明（Why You Are Requesting `instagram_business_basic`）

作为 **你的产品名称**，我们是一款**客服 / 内容聚合 / 业务仪表盘**工具，帮助专业账号用户统一管理 Instagram 业务账户的信息与动态：

- 在用户连接 Instagram Business／Creator 账号后，通过此权限读取账号的基础信息（username、user_id、profile_picture）与媒体总数（media_count）、粉丝数等；
- 将这些资料用于界面展示与 agent 分配，确保消息来源清晰、有迹可循；
- 与其他权限如 `instagram_business_manage_messages` 与 `instagram_business_manage_comments` 联动，实现完整的消息支持流程。

**具体范围包括：**

- 获取 IG 业务账号 **username**、**account ID**、**profile_picture_url**、**followers_count**、**media_count**；
- 获取所有媒体的列表及媒体基本信息（`id`, `caption`, `media_type`, `media_url`, `permalink`, `timestamp`）；
- 全部数据仅用于阅读展示，**非发布性质**，也不会透露给第三方。

| 项目 | 描述 |
|------|------|
| App URL | [你的产品登录页] |
| 测试账号 | 邮箱：[<EMAIL>] / 密码：[Test1234!] |
| Instagram 测试账号 | 【已绑定 Facebook 页面，完成 Business 连接】 |

---

# 2. Screencast 录屏演示脚本说明（Screencast Walkthrough）

> **（请确保录制实机操作流程，视频分辨率建议 1280×720 以上，录屏中不必出现调试面板，仅需展示用户可见 UI）**

### 🪄 视频操作流程

1. 使用上述测试账号登录产品控制台（Dashboard）。
2. 首次登录时进入「设置 → 添加 Instagram 业务账户」界面。
3. 点击 **“继续使用 Instagram”**，触发 Instagram 登录弹窗，输入账号凭据并授权本应用（scope 包含 instagram_business_basic）。
4. 回到 Dashboard，展示已连接的 IG Business 帐号列表，包括 avatar、username、business ID。
5. 点击某账号进入详情页，后台依次调用如下接口：
   - `GET /{ig-user-id}?fields=username,profile_picture_url,id,followers_count,media_count&access_token={token}`
   - `GET /{ig-user-id}/media?fields=id,caption,media_type,media_url,permalink,timestamp`
6. 等接口返回数据后，前端展示如下内容：
   - 显示 `username`, `profile_picture_url`, `followers_count`, `media_count`
   - 显示照片与 Reels 格式的媒体列表（media_url + caption + timestamp）
7. 演示产品重试数据拉取流程：
   1. 点击「刷新数据 Spinner」按钮；
   2. 后台再次调用上述接口，页面实时刷新；
   3. 登录 Instagram 的任务是否读取成功与更新情况；
   4. 如果用户删除一篇帖子，Dashboard 即刻反映出媒体数量减少；
8. 演示 2 次可选增强流程（可自主选录）：
   - 同时触发 `instagram_business_manage_comments`（如在帖子下评论）；
   - 登录后台查看分页媒体 list 分页正常工作。

---

# 3. 注意事项与审核提示（Reviewer Notes）
